const express = require('express');
const sqlite3 = require('sqlite3').verbose();
const bcrypt = require('bcryptjs');
const session = require('express-session');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const puppeteer = require('puppeteer');

const app = express();
const PORT = process.env.PORT || 3000;

// إعداد قاعدة البيانات
const db = new sqlite3.Database('./magazine.db');

// إعداد الجلسات
app.use(session({
    secret: 'magazine-secret-key-2025',
    resave: false,
    saveUninitialized: false,
    cookie: { secure: false, maxAge: 24 * 60 * 60 * 1000 } // 24 ساعة
}));

// إعداد محرك القوالب
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));

// إعداد الملفات الثابتة
app.use(express.static('public'));
app.use('/uploads', express.static('uploads'));

// إعداد معالجة البيانات
app.use(express.urlencoded({ extended: true }));
app.use(express.json());

// إعداد رفع الملفات
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        const uploadDir = 'uploads/';
        if (!fs.existsSync(uploadDir)) {
            fs.mkdirSync(uploadDir, { recursive: true });
        }
        cb(null, uploadDir);
    },
    filename: function (req, file, cb) {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, uniqueSuffix + path.extname(file.originalname));
    }
});

const upload = multer({ 
    storage: storage,
    fileFilter: function (req, file, cb) {
        if (file.mimetype.startsWith('image/')) {
            cb(null, true);
        } else {
            cb(new Error('يجب أن يكون الملف صورة'));
        }
    },
    limits: { fileSize: 5 * 1024 * 1024 } // 5MB
});

// Middleware للتحقق من تسجيل الدخول
const requireAuth = (req, res, next) => {
    if (req.session.userId) {
        next();
    } else {
        res.redirect('/admin/login');
    }
};

// Middleware للتحقق من صلاحيات المدير
const requireAdmin = (req, res, next) => {
    if (req.session.userId && req.session.userRole === 'admin') {
        next();
    } else {
        res.status(403).send('غير مصرح لك بالوصول لهذه الصفحة');
    }
};

// إنشاء الجداول
db.serialize(() => {
    // جدول المستخدمين
    db.run(`CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username TEXT UNIQUE NOT NULL,
        email TEXT UNIQUE NOT NULL,
        password TEXT NOT NULL,
        role TEXT DEFAULT 'editor',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )`);

    // جدول الأخبار
    db.run(`CREATE TABLE IF NOT EXISTS news (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT NOT NULL,
        content TEXT NOT NULL,
        image_path TEXT,
        category TEXT,
        author_id INTEGER,
        issue_id INTEGER,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (author_id) REFERENCES users (id),
        FOREIGN KEY (issue_id) REFERENCES issues (id)
    )`);

    // جدول الأعداد
    db.run(`CREATE TABLE IF NOT EXISTS issues (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT NOT NULL,
        description TEXT,
        cover_image TEXT,
        issue_date DATE,
        status TEXT DEFAULT 'draft',
        created_by INTEGER,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        published_at DATETIME,
        FOREIGN KEY (created_by) REFERENCES users (id)
    )`);

    // جدول المشتركين
    db.run(`CREATE TABLE IF NOT EXISTS subscribers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        email TEXT UNIQUE NOT NULL,
        name TEXT,
        status TEXT DEFAULT 'active',
        subscribed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        unsubscribed_at DATETIME
    )`);

    // إنشاء مستخدم مدير افتراضي
    const adminPassword = bcrypt.hashSync('admin123', 10);
    db.run(`INSERT OR IGNORE INTO users (username, email, password, role) 
            VALUES ('admin', '<EMAIL>', ?, 'admin')`, [adminPassword]);
});

// الصفحة الرئيسية
app.get('/', (req, res) => {
    db.all(`SELECT i.*, COUNT(n.id) as news_count
            FROM issues i
            LEFT JOIN news n ON i.id = n.issue_id
            WHERE i.status = 'published'
            GROUP BY i.id
            ORDER BY i.issue_date DESC LIMIT 6`, (err, issues) => {
        if (err) {
            console.error(err);
            return res.status(500).send('خطأ في قاعدة البيانات');
        }
        res.render('index', { issues });
    });
});

// صفحة الأرشيف
app.get('/archive', (req, res) => {
    db.all(`SELECT i.*, COUNT(n.id) as news_count
            FROM issues i
            LEFT JOIN news n ON i.id = n.issue_id
            WHERE i.status = 'published'
            GROUP BY i.id
            ORDER BY i.issue_date DESC`, (err, issues) => {
        if (err) {
            console.error(err);
            return res.status(500).send('خطأ في قاعدة البيانات');
        }
        res.render('archive', { issues });
    });
});

// صفحة حول المجلة
app.get('/about', (req, res) => {
    res.render('about');
});

// صفحة الاشتراك
app.get('/subscribe', (req, res) => {
    res.render('subscribe', { message: null, error: null });
});

// معالجة الاشتراك
app.post('/subscribe', (req, res) => {
    const { email, name } = req.body;

    if (!email) {
        return res.render('subscribe', {
            message: null,
            error: 'البريد الإلكتروني مطلوب'
        });
    }

    // التحقق من وجود البريد مسبقاً
    db.get('SELECT * FROM subscribers WHERE email = ?', [email], (err, existing) => {
        if (err) {
            console.error(err);
            return res.render('subscribe', {
                message: null,
                error: 'حدث خطأ في النظام'
            });
        }

        if (existing) {
            if (existing.status === 'active') {
                return res.render('subscribe', {
                    message: null,
                    error: 'هذا البريد الإلكتروني مشترك بالفعل'
                });
            } else {
                // إعادة تفعيل الاشتراك
                db.run('UPDATE subscribers SET status = "active", unsubscribed_at = NULL WHERE email = ?',
                       [email], (err) => {
                    if (err) {
                        console.error(err);
                        return res.render('subscribe', {
                            message: null,
                            error: 'حدث خطأ في النظام'
                        });
                    }
                    res.render('subscribe', {
                        message: 'تم تجديد اشتراكك بنجاح!',
                        error: null
                    });
                });
            }
        } else {
            // إضافة مشترك جديد
            db.run('INSERT INTO subscribers (email, name) VALUES (?, ?)',
                   [email, name || null], (err) => {
                if (err) {
                    console.error(err);
                    return res.render('subscribe', {
                        message: null,
                        error: 'حدث خطأ في النظام'
                    });
                }
                res.render('subscribe', {
                    message: 'تم الاشتراك بنجاح! ستصلك الأعداد الجديدة على بريدك الإلكتروني.',
                    error: null
                });
            });
        }
    });
});

// إلغاء الاشتراك
app.get('/unsubscribe/:email', (req, res) => {
    const email = decodeURIComponent(req.params.email);

    db.run('UPDATE subscribers SET status = "inactive", unsubscribed_at = CURRENT_TIMESTAMP WHERE email = ?',
           [email], (err) => {
        if (err) {
            console.error(err);
            return res.status(500).send('حدث خطأ في النظام');
        }
        res.render('unsubscribe', { email });
    });
});

// إدارة المشتركين (للمدراء فقط)
app.get('/admin/subscribers', requireAdmin, (req, res) => {
    db.all('SELECT * FROM subscribers ORDER BY subscribed_at DESC', (err, subscribers) => {
        if (err) {
            console.error(err);
            return res.status(500).send('خطأ في قاعدة البيانات');
        }
        res.render('admin/subscribers', { user: req.session, subscribers });
    });
});

// صفحة تسجيل الدخول
app.get('/admin/login', (req, res) => {
    res.render('admin/login', { error: null });
});

app.post('/admin/login', (req, res) => {
    const { username, password } = req.body;
    
    db.get('SELECT * FROM users WHERE username = ?', [username], (err, user) => {
        if (err) {
            return res.render('admin/login', { error: 'خطأ في قاعدة البيانات' });
        }
        
        if (!user || !bcrypt.compareSync(password, user.password)) {
            return res.render('admin/login', { error: 'اسم المستخدم أو كلمة المرور غير صحيحة' });
        }
        
        req.session.userId = user.id;
        req.session.username = user.username;
        req.session.userRole = user.role;
        
        res.redirect('/admin/dashboard');
    });
});

// تسجيل الخروج
app.get('/admin/logout', (req, res) => {
    req.session.destroy();
    res.redirect('/');
});

// لوحة التحكم
app.get('/admin/dashboard', requireAuth, (req, res) => {
    // إحصائيات سريعة
    db.get('SELECT COUNT(*) as count FROM news', (err, newsCount) => {
        db.get('SELECT COUNT(*) as count FROM issues', (err2, issuesCount) => {
            db.get('SELECT COUNT(*) as count FROM users', (err3, usersCount) => {
                res.render('admin/dashboard', {
                    user: req.session,
                    stats: {
                        news: newsCount ? newsCount.count : 0,
                        issues: issuesCount ? issuesCount.count : 0,
                        users: usersCount ? usersCount.count : 0
                    }
                });
            });
        });
    });
});

// إدارة الأخبار
app.get('/admin/news', requireAuth, (req, res) => {
    db.all(`SELECT n.*, u.username as author_name, i.title as issue_title
            FROM news n
            LEFT JOIN users u ON n.author_id = u.id
            LEFT JOIN issues i ON n.issue_id = i.id
            ORDER BY n.created_at DESC`, (err, news) => {
        if (err) {
            console.error(err);
            return res.status(500).send('خطأ في قاعدة البيانات');
        }
        res.render('admin/news/index', { user: req.session, news });
    });
});

app.get('/admin/news/create', requireAuth, (req, res) => {
    db.all('SELECT * FROM issues ORDER BY created_at DESC', (err, issues) => {
        if (err) {
            console.error(err);
            return res.status(500).send('خطأ في قاعدة البيانات');
        }
        res.render('admin/news/create', { user: req.session, issues, error: null });
    });
});

app.post('/admin/news/create', requireAuth, upload.single('image'), (req, res) => {
    const { title, content, category, issue_id } = req.body;
    const image_path = req.file ? req.file.filename : null;

    if (!title || !content) {
        return res.render('admin/news/create', {
            user: req.session,
            issues: [],
            error: 'العنوان والمحتوى مطلوبان'
        });
    }

    db.run(`INSERT INTO news (title, content, image_path, category, author_id, issue_id)
            VALUES (?, ?, ?, ?, ?, ?)`,
            [title, content, image_path, category, req.session.userId, issue_id || null],
            function(err) {
        if (err) {
            console.error(err);
            return res.status(500).send('خطأ في إضافة الخبر');
        }
        res.redirect('/admin/news');
    });
});

// إدارة الأعداد
app.get('/admin/issues', requireAuth, (req, res) => {
    db.all(`SELECT i.*, u.username as created_by_name, COUNT(n.id) as news_count
            FROM issues i
            LEFT JOIN users u ON i.created_by = u.id
            LEFT JOIN news n ON i.id = n.issue_id
            GROUP BY i.id
            ORDER BY i.created_at DESC`, (err, issues) => {
        if (err) {
            console.error(err);
            return res.status(500).send('خطأ في قاعدة البيانات');
        }
        res.render('admin/issues/index', { user: req.session, issues });
    });
});

app.get('/admin/issues/create', requireAuth, (req, res) => {
    res.render('admin/issues/create', { user: req.session, error: null });
});

app.post('/admin/issues/create', requireAuth, upload.single('cover_image'), (req, res) => {
    const { title, description, issue_date } = req.body;
    const cover_image = req.file ? req.file.filename : null;

    if (!title) {
        return res.render('admin/issues/create', {
            user: req.session,
            error: 'عنوان العدد مطلوب'
        });
    }

    db.run(`INSERT INTO issues (title, description, cover_image, issue_date, created_by)
            VALUES (?, ?, ?, ?, ?)`,
            [title, description, cover_image, issue_date, req.session.userId],
            function(err) {
        if (err) {
            console.error(err);
            return res.status(500).send('خطأ في إنشاء العدد');
        }
        res.redirect('/admin/issues');
    });
});

// نشر العدد
app.post('/admin/issues/:id/publish', requireAuth, (req, res) => {
    const issueId = req.params.id;

    db.run(`UPDATE issues SET status = 'published', published_at = CURRENT_TIMESTAMP
            WHERE id = ?`, [issueId], function(err) {
        if (err) {
            console.error(err);
            return res.status(500).send('خطأ في نشر العدد');
        }
        res.redirect('/admin/issues');
    });
});

// عرض العدد للجمهور
app.get('/issues/:id', (req, res) => {
    const issueId = req.params.id;

    db.get(`SELECT * FROM issues WHERE id = ? AND status = 'published'`, [issueId], (err, issue) => {
        if (err) {
            console.error(err);
            return res.status(500).send('خطأ في قاعدة البيانات');
        }

        if (!issue) {
            return res.status(404).send('العدد غير موجود أو غير منشور');
        }

        db.all(`SELECT * FROM news WHERE issue_id = ? ORDER BY created_at DESC`, [issueId], (err, news) => {
            if (err) {
                console.error(err);
                return res.status(500).send('خطأ في قاعدة البيانات');
            }

            res.render('issues/view', { issue, news });
        });
    });
});

// تحميل العدد كـ PDF
app.get('/issues/:id/pdf', async (req, res) => {
    const issueId = req.params.id;

    try {
        const issue = await new Promise((resolve, reject) => {
            db.get(`SELECT * FROM issues WHERE id = ? AND status = 'published'`, [issueId], (err, result) => {
                if (err) reject(err);
                else resolve(result);
            });
        });

        if (!issue) {
            return res.status(404).send('العدد غير موجود أو غير منشور');
        }

        const news = await new Promise((resolve, reject) => {
            db.all(`SELECT * FROM news WHERE issue_id = ? ORDER BY created_at DESC`, [issueId], (err, result) => {
                if (err) reject(err);
                else resolve(result);
            });
        });

        // توليد PDF باستخدام Puppeteer
        const browser = await puppeteer.launch({
            headless: 'new',
            args: ['--no-sandbox', '--disable-setuid-sandbox', '--disable-dev-shm-usage']
        });

        const page = await browser.newPage();

        // إنشاء HTML مباشرة
        const html = `
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <style>
                body { font-family: Arial, sans-serif; direction: rtl; }
                .header { background: #667eea; color: white; padding: 20px; text-align: center; }
                .news-item { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
                .news-title { font-size: 18px; font-weight: bold; margin-bottom: 10px; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>${issue.title}</h1>
                <p>تاريخ الإصدار: ${new Date(issue.issue_date).toLocaleDateString('ar-SA')}</p>
            </div>
            ${news.map(item => `
                <div class="news-item">
                    <div class="news-title">${item.title}</div>
                    <div>${item.content}</div>
                </div>
            `).join('')}
        </body>
        </html>`;

        await page.setContent(html, { waitUntil: 'networkidle0' });

        const pdf = await page.pdf({
            format: 'A4',
            printBackground: true,
            margin: { top: '20mm', right: '20mm', bottom: '20mm', left: '20mm' }
        });

        await browser.close();

        res.setHeader('Content-Type', 'application/pdf');
        res.setHeader('Content-Disposition', `attachment; filename="${issue.title}.pdf"`);
        res.send(pdf);

    } catch (error) {
        console.error('خطأ في توليد PDF:', error);
        res.status(500).send('خطأ في توليد ملف PDF');
    }
});

// حذف خبر
app.post('/admin/news/:id/delete', requireAuth, (req, res) => {
    const newsId = req.params.id;

    // حذف الصورة أولاً إن وجدت
    db.get('SELECT image_path FROM news WHERE id = ?', [newsId], (err, news) => {
        if (news && news.image_path) {
            const imagePath = path.join(__dirname, 'uploads', news.image_path);
            if (fs.existsSync(imagePath)) {
                fs.unlinkSync(imagePath);
            }
        }

        // حذف الخبر من قاعدة البيانات
        db.run('DELETE FROM news WHERE id = ?', [newsId], function(err) {
            if (err) {
                console.error(err);
                return res.status(500).send('خطأ في حذف الخبر');
            }
            res.redirect('/admin/news');
        });
    });
});

// تعديل خبر
app.get('/admin/news/:id/edit', requireAuth, (req, res) => {
    const newsId = req.params.id;

    db.get('SELECT * FROM news WHERE id = ?', [newsId], (err, news) => {
        if (err) {
            console.error(err);
            return res.status(500).send('خطأ في قاعدة البيانات');
        }

        if (!news) {
            return res.status(404).send('الخبر غير موجود');
        }

        db.all('SELECT * FROM issues ORDER BY created_at DESC', (err, issues) => {
            if (err) {
                console.error(err);
                return res.status(500).send('خطأ في قاعدة البيانات');
            }
            res.render('admin/news/edit', { user: req.session, news, issues, error: null });
        });
    });
});

app.post('/admin/news/:id/edit', requireAuth, upload.single('image'), (req, res) => {
    const newsId = req.params.id;
    const { title, content, category, issue_id } = req.body;
    let image_path = req.body.current_image;

    if (req.file) {
        // حذف الصورة القديمة
        if (image_path) {
            const oldImagePath = path.join(__dirname, 'uploads', image_path);
            if (fs.existsSync(oldImagePath)) {
                fs.unlinkSync(oldImagePath);
            }
        }
        image_path = req.file.filename;
    }

    db.run(`UPDATE news SET title = ?, content = ?, image_path = ?, category = ?, issue_id = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?`,
            [title, content, image_path, category, issue_id || null, newsId],
            function(err) {
        if (err) {
            console.error(err);
            return res.status(500).send('خطأ في تحديث الخبر');
        }
        res.redirect('/admin/news');
    });
});

// حذف عدد
app.post('/admin/issues/:id/delete', requireAuth, (req, res) => {
    const issueId = req.params.id;

    // حذف صورة الغلاف أولاً إن وجدت
    db.get('SELECT cover_image FROM issues WHERE id = ?', [issueId], (err, issue) => {
        if (issue && issue.cover_image) {
            const imagePath = path.join(__dirname, 'uploads', issue.cover_image);
            if (fs.existsSync(imagePath)) {
                fs.unlinkSync(imagePath);
            }
        }

        // إلغاء ربط الأخبار بالعدد
        db.run('UPDATE news SET issue_id = NULL WHERE issue_id = ?', [issueId], (err) => {
            // حذف العدد من قاعدة البيانات
            db.run('DELETE FROM issues WHERE id = ?', [issueId], function(err) {
                if (err) {
                    console.error(err);
                    return res.status(500).send('خطأ في حذف العدد');
                }
                res.redirect('/admin/issues');
            });
        });
    });
});

// بدء الخادم
app.listen(PORT, () => {
    console.log(`الخادم يعمل على المنفذ ${PORT}`);
    console.log(`يمكنك الوصول للموقع على: http://localhost:${PORT}`);
    console.log(`لوحة التحكم: http://localhost:${PORT}/admin/login`);
    console.log(`المستخدم الافتراضي: admin / admin123`);
});
