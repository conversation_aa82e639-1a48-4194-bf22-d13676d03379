<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تعديل الخبر - لوحة التحكم</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold" href="/admin/dashboard">
                <i class="fas fa-tachometer-alt me-2"></i>
                لوحة التحكم
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/dashboard">
                            <i class="fas fa-home me-1"></i>
                            الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/admin/news">
                            <i class="fas fa-newspaper me-1"></i>
                            الأخبار
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/issues">
                            <i class="fas fa-book me-1"></i>
                            الأعداد
                        </a>
                    </li>
                    <% if (user.userRole === 'admin') { %>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/users">
                            <i class="fas fa-users me-1"></i>
                            المستخدمين
                        </a>
                    </li>
                    <% } %>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>
                            <%= user.username %>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/" target="_blank">
                                <i class="fas fa-external-link-alt me-2"></i>
                                عرض الموقع
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/admin/logout">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                تسجيل الخروج
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="h3 fw-bold mb-2">
                            <i class="fas fa-edit text-primary me-2"></i>
                            تعديل الخبر
                        </h1>
                        <p class="text-muted mb-0">تعديل تفاصيل الخبر</p>
                    </div>
                    <div>
                        <a href="/admin/news" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-right me-2"></i>
                            العودة للقائمة
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Error Message -->
        <% if (error) { %>
            <div class="row mb-4">
                <div class="col-12">
                    <div class="alert alert-danger" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <%= error %>
                    </div>
                </div>
            </div>
        <% } %>

        <!-- News Form -->
        <div class="row">
            <div class="col-lg-8">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-transparent border-0">
                        <h5 class="fw-bold mb-0">
                            <i class="fas fa-edit me-2"></i>
                            تفاصيل الخبر
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="/admin/news/<%= news.id %>/edit" enctype="multipart/form-data">
                            <input type="hidden" name="current_image" value="<%= news.image_path || '' %>">
                            
                            <div class="row">
                                <div class="col-12 mb-3">
                                    <label for="title" class="form-label">
                                        <i class="fas fa-heading me-2"></i>
                                        عنوان الخبر *
                                    </label>
                                    <input type="text" 
                                           class="form-control" 
                                           id="title" 
                                           name="title" 
                                           required 
                                           value="<%= news.title %>"
                                           placeholder="أدخل عنوان الخبر">
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="category" class="form-label">
                                        <i class="fas fa-tag me-2"></i>
                                        فئة الخبر
                                    </label>
                                    <select class="form-select" id="category" name="category">
                                        <option value="">اختر الفئة</option>
                                        <option value="أخبار عامة" <%= news.category === 'أخبار عامة' ? 'selected' : '' %>>أخبار عامة</option>
                                        <option value="فعاليات" <%= news.category === 'فعاليات' ? 'selected' : '' %>>فعاليات</option>
                                        <option value="إنجازات" <%= news.category === 'إنجازات' ? 'selected' : '' %>>إنجازات</option>
                                        <option value="إعلانات" <%= news.category === 'إعلانات' ? 'selected' : '' %>>إعلانات</option>
                                        <option value="مؤتمرات" <%= news.category === 'مؤتمرات' ? 'selected' : '' %>>مؤتمرات</option>
                                        <option value="ورش عمل" <%= news.category === 'ورش عمل' ? 'selected' : '' %>>ورش عمل</option>
                                    </select>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="issue_id" class="form-label">
                                        <i class="fas fa-book me-2"></i>
                                        ربط بعدد المجلة
                                    </label>
                                    <select class="form-select" id="issue_id" name="issue_id">
                                        <option value="">بدون ربط</option>
                                        <% issues.forEach(issue => { %>
                                            <option value="<%= issue.id %>" <%= news.issue_id == issue.id ? 'selected' : '' %>>
                                                <%= issue.title %>
                                            </option>
                                        <% }); %>
                                    </select>
                                </div>

                                <div class="col-12 mb-3">
                                    <label for="content" class="form-label">
                                        <i class="fas fa-align-right me-2"></i>
                                        محتوى الخبر *
                                    </label>
                                    <textarea class="form-control" 
                                              id="content" 
                                              name="content" 
                                              rows="8" 
                                              required 
                                              placeholder="اكتب محتوى الخبر هنا..."><%= news.content %></textarea>
                                </div>

                                <div class="col-12 mb-4">
                                    <label for="image" class="form-label">
                                        <i class="fas fa-image me-2"></i>
                                        صورة الخبر
                                    </label>
                                    
                                    <% if (news.image_path) { %>
                                        <div class="mb-3">
                                            <img src="/uploads/<%= news.image_path %>" 
                                                 alt="الصورة الحالية" 
                                                 class="img-thumbnail"
                                                 style="max-width: 200px; max-height: 150px;">
                                            <div class="form-text">الصورة الحالية</div>
                                        </div>
                                    <% } %>
                                    
                                    <input type="file" 
                                           class="form-control" 
                                           id="image" 
                                           name="image" 
                                           accept="image/*"
                                           data-preview="imagePreview">
                                    <div class="form-text">
                                        <i class="fas fa-info-circle me-1"></i>
                                        اختر صورة جديدة لاستبدال الصورة الحالية (اختياري)
                                    </div>
                                    <img id="imagePreview" 
                                         src="#" 
                                         alt="معاينة الصورة الجديدة" 
                                         class="mt-3 rounded" 
                                         style="max-width: 300px; max-height: 200px; display: none;">
                                </div>

                                <div class="col-12">
                                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                        <a href="/admin/news" class="btn btn-outline-secondary me-md-2">
                                            <i class="fas fa-times me-2"></i>
                                            إلغاء
                                        </a>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-2"></i>
                                            حفظ التغييرات
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-transparent border-0">
                        <h6 class="fw-bold mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            معلومات الخبر
                        </h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <strong>تاريخ الإنشاء:</strong><br>
                                <small class="text-muted">
                                    <%= new Date(news.created_at).toLocaleDateString('ar-SA') %>
                                </small>
                            </li>
                            <li class="mb-2">
                                <strong>آخر تحديث:</strong><br>
                                <small class="text-muted">
                                    <%= new Date(news.updated_at).toLocaleDateString('ar-SA') %>
                                </small>
                            </li>
                            <li class="mb-0">
                                <strong>رقم الخبر:</strong><br>
                                <small class="text-muted">#<%= news.id %></small>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="/js/main.js"></script>
</body>
</html>
