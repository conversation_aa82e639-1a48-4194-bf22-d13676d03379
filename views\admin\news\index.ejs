<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الأخبار - لوحة التحكم</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold" href="/admin/dashboard">
                <i class="fas fa-tachometer-alt me-2"></i>
                لوحة التحكم
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/dashboard">
                            <i class="fas fa-home me-1"></i>
                            الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/admin/news">
                            <i class="fas fa-newspaper me-1"></i>
                            الأخبار
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/issues">
                            <i class="fas fa-book me-1"></i>
                            الأعداد
                        </a>
                    </li>
                    <% if (user.userRole === 'admin') { %>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/users">
                            <i class="fas fa-users me-1"></i>
                            المستخدمين
                        </a>
                    </li>
                    <% } %>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>
                            <%= user.username %>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/" target="_blank">
                                <i class="fas fa-external-link-alt me-2"></i>
                                عرض الموقع
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/admin/logout">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                تسجيل الخروج
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="h3 fw-bold mb-2">
                            <i class="fas fa-newspaper text-primary me-2"></i>
                            إدارة الأخبار
                        </h1>
                        <p class="text-muted mb-0">إضافة وتحرير وإدارة أخبار المؤسسة</p>
                    </div>
                    <div>
                        <a href="/admin/news/create" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>
                            إضافة خبر جديد
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- News List -->
        <div class="row">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-transparent border-0">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="fw-bold mb-0">
                                <i class="fas fa-list me-2"></i>
                                قائمة الأخبار
                            </h5>
                            <span class="badge bg-primary rounded-pill">
                                <%= news.length %> خبر
                            </span>
                        </div>
                    </div>
                    <div class="card-body">
                        <% if (news && news.length > 0) { %>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>الصورة</th>
                                            <th>العنوان</th>
                                            <th>الفئة</th>
                                            <th>الكاتب</th>
                                            <th>العدد</th>
                                            <th>تاريخ الإنشاء</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <% news.forEach(item => { %>
                                            <tr>
                                                <td>
                                                    <% if (item.image_path) { %>
                                                        <img src="/uploads/<%= item.image_path %>" 
                                                             alt="<%= item.title %>" 
                                                             class="rounded" 
                                                             style="width: 60px; height: 60px; object-fit: cover;">
                                                    <% } else { %>
                                                        <div class="bg-light rounded d-flex align-items-center justify-content-center" 
                                                             style="width: 60px; height: 60px;">
                                                            <i class="fas fa-image text-muted"></i>
                                                        </div>
                                                    <% } %>
                                                </td>
                                                <td>
                                                    <div class="fw-bold"><%= item.title %></div>
                                                    <div class="text-muted small">
                                                        <%= item.content.substring(0, 50) %>...
                                                    </div>
                                                </td>
                                                <td>
                                                    <% if (item.category) { %>
                                                        <span class="badge bg-secondary"><%= item.category %></span>
                                                    <% } else { %>
                                                        <span class="text-muted">غير محدد</span>
                                                    <% } %>
                                                </td>
                                                <td><%= item.author_name || 'غير معروف' %></td>
                                                <td>
                                                    <% if (item.issue_title) { %>
                                                        <span class="badge bg-info"><%= item.issue_title %></span>
                                                    <% } else { %>
                                                        <span class="text-muted">غير مرتبط</span>
                                                    <% } %>
                                                </td>
                                                <td>
                                                    <small class="text-muted">
                                                        <%= new Date(item.created_at).toLocaleDateString('ar-SA') %>
                                                    </small>
                                                </td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <a href="/admin/news/<%= item.id %>/edit"
                                                           class="btn btn-sm btn-outline-primary"
                                                           title="تعديل">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                        <form method="POST"
                                                              action="/admin/news/<%= item.id %>/delete"
                                                              style="display: inline;"
                                                              onsubmit="return confirm('هل أنت متأكد من حذف هذا الخبر؟')">
                                                            <button type="submit"
                                                                    class="btn btn-sm btn-outline-danger"
                                                                    title="حذف">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </form>
                                                    </div>
                                                </td>
                                            </tr>
                                        <% }); %>
                                    </tbody>
                                </table>
                            </div>
                        <% } else { %>
                            <div class="text-center py-5">
                                <i class="fas fa-newspaper fa-5x text-muted mb-4"></i>
                                <h5 class="text-muted mb-3">لا توجد أخبار حالياً</h5>
                                <p class="text-muted">ابدأ بإضافة أول خبر للمؤسسة</p>
                                <a href="/admin/news/create" class="btn btn-primary">
                                    <i class="fas fa-plus me-2"></i>
                                    إضافة خبر جديد
                                </a>
                            </div>
                        <% } %>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="/js/main.js"></script>
</body>
</html>
