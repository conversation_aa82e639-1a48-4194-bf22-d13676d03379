<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= issue.title %> - المجلة الإلكترونية</title>
    
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: '<PERSON><PERSON><PERSON>', Arial, sans-serif;
            direction: rtl;
            text-align: right;
            line-height: 1.6;
            color: #333;
            background: white;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .header .subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            margin-bottom: 20px;
        }
        
        .header .meta {
            font-size: 1rem;
            opacity: 0.8;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        .issue-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            border-right: 5px solid #667eea;
        }
        
        .issue-info h2 {
            color: #667eea;
            font-size: 1.5rem;
            margin-bottom: 10px;
        }
        
        .issue-info p {
            color: #6c757d;
            margin-bottom: 10px;
        }
        
        .news-section {
            margin-bottom: 40px;
        }
        
        .news-section h2 {
            color: #2c3e50;
            font-size: 2rem;
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 10px;
            border-bottom: 3px solid #667eea;
        }
        
        .news-item {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            page-break-inside: avoid;
        }
        
        .news-item .category {
            background: #667eea;
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            display: inline-block;
            margin-bottom: 15px;
        }
        
        .news-item h3 {
            color: #2c3e50;
            font-size: 1.4rem;
            font-weight: 600;
            margin-bottom: 15px;
            line-height: 1.4;
        }
        
        .news-item .content {
            color: #495057;
            font-size: 1rem;
            line-height: 1.7;
            margin-bottom: 15px;
        }
        
        .news-item .content p {
            margin-bottom: 10px;
        }
        
        .news-item .meta {
            color: #6c757d;
            font-size: 0.9rem;
            padding-top: 15px;
            border-top: 1px solid #e9ecef;
        }
        
        .news-item img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .footer {
            background: #2c3e50;
            color: white;
            padding: 20px;
            text-align: center;
            margin-top: 40px;
        }
        
        .footer p {
            margin: 0;
            font-size: 0.9rem;
        }
        
        .page-break {
            page-break-before: always;
        }
        
        .no-print {
            display: none;
        }
        
        @media print {
            .header {
                background: #667eea !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }
            
            .news-item {
                box-shadow: none;
                border: 1px solid #ddd;
            }
            
            .footer {
                background: #2c3e50 !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <h1><%= issue.title %></h1>
        <div class="subtitle">المجلة الإلكترونية</div>
        <div class="meta">
            تاريخ الإصدار: <%= new Date(issue.issue_date).toLocaleDateString('ar-SA', { 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
            }) %> | عدد الأخبار: <%= news.length %>
        </div>
    </div>

    <div class="container">
        <!-- Issue Information -->
        <% if (issue.description) { %>
            <div class="issue-info">
                <h2>نبذة عن العدد</h2>
                <p><%= issue.description %></p>
            </div>
        <% } %>

        <!-- News Section -->
        <% if (news && news.length > 0) { %>
            <div class="news-section">
                <h2>محتويات العدد</h2>
                
                <% news.forEach((item, index) => { %>
                    <article class="news-item">
                        <% if (item.category) { %>
                            <div class="category"><%= item.category %></div>
                        <% } %>
                        
                        <h3><%= item.title %></h3>
                        
                        <% if (item.image_path) { %>
                            <img src="<%= 'http://localhost:3000/uploads/' + item.image_path %>" 
                                 alt="<%= item.title %>">
                        <% } %>
                        
                        <div class="content">
                            <% 
                                // تحويل النص إلى فقرات
                                const paragraphs = item.content.split('\n').filter(p => p.trim());
                                paragraphs.forEach(paragraph => {
                            %>
                                <p><%= paragraph %></p>
                            <% }); %>
                        </div>
                        
                        <div class="meta">
                            تاريخ النشر: <%= new Date(item.created_at).toLocaleDateString('ar-SA') %>
                        </div>
                    </article>
                    
                    <!-- فاصل صفحة بعد كل 3 أخبار -->
                    <% if ((index + 1) % 3 === 0 && index < news.length - 1) { %>
                        <div class="page-break"></div>
                    <% } %>
                <% }); %>
            </div>
        <% } else { %>
            <div class="news-section">
                <h2>محتويات العدد</h2>
                <div class="news-item">
                    <h3>لا توجد أخبار في هذا العدد</h3>
                    <div class="content">
                        <p>هذا العدد لا يحتوي على أخبار حالياً. سيتم إضافة المحتوى في الإصدارات القادمة.</p>
                    </div>
                </div>
            </div>
        <% } %>
    </div>

    <!-- Footer -->
    <div class="footer">
        <p>&copy; <%= new Date().getFullYear() %> جميع الحقوق محفوظة - المجلة الإلكترونية</p>
    </div>
</body>
</html>
