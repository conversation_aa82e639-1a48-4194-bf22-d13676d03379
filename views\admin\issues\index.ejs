<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الأعداد - لوحة التحكم</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold" href="/admin/dashboard">
                <i class="fas fa-tachometer-alt me-2"></i>
                لوحة التحكم
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/dashboard">
                            <i class="fas fa-home me-1"></i>
                            الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/news">
                            <i class="fas fa-newspaper me-1"></i>
                            الأخبار
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/admin/issues">
                            <i class="fas fa-book me-1"></i>
                            الأعداد
                        </a>
                    </li>
                    <% if (user.userRole === 'admin') { %>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/users">
                            <i class="fas fa-users me-1"></i>
                            المستخدمين
                        </a>
                    </li>
                    <% } %>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>
                            <%= user.username %>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/" target="_blank">
                                <i class="fas fa-external-link-alt me-2"></i>
                                عرض الموقع
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/admin/logout">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                تسجيل الخروج
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="h3 fw-bold mb-2">
                            <i class="fas fa-book text-primary me-2"></i>
                            إدارة الأعداد
                        </h1>
                        <p class="text-muted mb-0">إنشاء وإدارة أعداد المجلة الإلكترونية</p>
                    </div>
                    <div>
                        <a href="/admin/issues/create" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>
                            إنشاء عدد جديد
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Issues List -->
        <div class="row">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-transparent border-0">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="fw-bold mb-0">
                                <i class="fas fa-list me-2"></i>
                                قائمة الأعداد
                            </h5>
                            <span class="badge bg-primary rounded-pill">
                                <%= issues.length %> عدد
                            </span>
                        </div>
                    </div>
                    <div class="card-body">
                        <% if (issues && issues.length > 0) { %>
                            <div class="row">
                                <% issues.forEach(issue => { %>
                                    <div class="col-lg-4 col-md-6 mb-4">
                                        <div class="card h-100 border-0 shadow-sm">
                                            <% if (issue.cover_image) { %>
                                                <img src="/uploads/<%= issue.cover_image %>" 
                                                     class="card-img-top" 
                                                     alt="<%= issue.title %>"
                                                     style="height: 200px; object-fit: cover;">
                                            <% } else { %>
                                                <div class="card-img-top d-flex align-items-center justify-content-center bg-light" 
                                                     style="height: 200px;">
                                                    <i class="fas fa-book fa-3x text-muted"></i>
                                                </div>
                                            <% } %>
                                            
                                            <div class="card-body d-flex flex-column">
                                                <h5 class="card-title fw-bold">
                                                    <%= issue.title %>
                                                </h5>
                                                
                                                <% if (issue.description) { %>
                                                    <p class="card-text text-muted flex-grow-1">
                                                        <%= issue.description.substring(0, 100) %>
                                                        <% if (issue.description.length > 100) { %>...<%}%>
                                                    </p>
                                                <% } %>
                                                
                                                <div class="mt-auto">
                                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                                        <small class="text-muted">
                                                            <i class="fas fa-calendar me-1"></i>
                                                            <%= issue.issue_date ? new Date(issue.issue_date).toLocaleDateString('ar-SA') : 'غير محدد' %>
                                                        </small>
                                                        <span class="badge bg-info rounded-pill">
                                                            <i class="fas fa-newspaper me-1"></i>
                                                            <%= issue.news_count %> خبر
                                                        </span>
                                                    </div>
                                                    
                                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                                        <small class="text-muted">
                                                            <i class="fas fa-user me-1"></i>
                                                            <%= issue.created_by_name || 'غير معروف' %>
                                                        </small>
                                                        <% if (issue.status === 'published') { %>
                                                            <span class="badge bg-success">منشور</span>
                                                        <% } else { %>
                                                            <span class="badge bg-warning">مسودة</span>
                                                        <% } %>
                                                    </div>
                                                    
                                                    <div class="d-grid gap-2">
                                                        <div class="btn-group" role="group">
                                                            <a href="/admin/issues/<%= issue.id %>/edit" 
                                                               class="btn btn-outline-primary btn-sm">
                                                                <i class="fas fa-edit"></i>
                                                                تحرير
                                                            </a>
                                                            <% if (issue.status === 'published') { %>
                                                                <a href="/issues/<%= issue.id %>" 
                                                                   class="btn btn-outline-success btn-sm"
                                                                   target="_blank">
                                                                    <i class="fas fa-eye"></i>
                                                                    عرض
                                                                </a>
                                                            <% } else { %>
                                                                <form method="POST" 
                                                                      action="/admin/issues/<%= issue.id %>/publish" 
                                                                      style="display: inline;">
                                                                    <button type="submit" 
                                                                            class="btn btn-outline-success btn-sm">
                                                                        <i class="fas fa-upload"></i>
                                                                        نشر
                                                                    </button>
                                                                </form>
                                                            <% } %>
                                                            <form method="POST"
                                                                  action="/admin/issues/<%= issue.id %>/delete"
                                                                  style="display: inline;"
                                                                  onsubmit="return confirm('هل أنت متأكد من حذف هذا العدد؟')">
                                                                <button type="submit"
                                                                        class="btn btn-outline-danger btn-sm"
                                                                        title="حذف">
                                                                    <i class="fas fa-trash"></i>
                                                                </button>
                                                            </form>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <% }); %>
                            </div>
                        <% } else { %>
                            <div class="text-center py-5">
                                <i class="fas fa-book fa-5x text-muted mb-4"></i>
                                <h5 class="text-muted mb-3">لا توجد أعداد حالياً</h5>
                                <p class="text-muted">ابدأ بإنشاء أول عدد للمجلة</p>
                                <a href="/admin/issues/create" class="btn btn-primary">
                                    <i class="fas fa-plus me-2"></i>
                                    إنشاء عدد جديد
                                </a>
                            </div>
                        <% } %>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="/js/main.js"></script>
</body>
</html>
