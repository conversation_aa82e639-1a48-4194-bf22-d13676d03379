<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= issue.title %> - المجلة الإلكترونية</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
        <div class="container">
            <a class="navbar-brand fw-bold" href="/">
                <i class="fas fa-newspaper me-2"></i>
                المجلة الإلكترونية
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">
                            <i class="fas fa-home me-1"></i>
                            الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/archive">
                            <i class="fas fa-archive me-1"></i>
                            الأرشيف
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/about">
                            <i class="fas fa-info-circle me-1"></i>
                            حول المجلة
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/login">
                            <i class="fas fa-sign-in-alt me-1"></i>
                            لوحة التحكم
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Issue Header -->
    <section class="py-5" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <div class="text-white">
                        <h1 class="display-4 fw-bold mb-3">
                            <%= issue.title %>
                        </h1>
                        <% if (issue.description) { %>
                            <p class="lead mb-4">
                                <%= issue.description %>
                            </p>
                        <% } %>
                        <div class="d-flex align-items-center">
                            <div class="me-4">
                                <i class="fas fa-calendar me-2"></i>
                                <%= new Date(issue.issue_date).toLocaleDateString('ar-SA', { 
                                    year: 'numeric', 
                                    month: 'long', 
                                    day: 'numeric' 
                                }) %>
                            </div>
                            <div class="me-4">
                                <i class="fas fa-newspaper me-2"></i>
                                <%= news.length %> خبر
                            </div>
                            <div>
                                <button class="btn btn-light btn-sm me-2" onclick="window.print()">
                                    <i class="fas fa-print me-2"></i>
                                    طباعة
                                </button>
                                <a href="/issues/<%= issue.id %>/pdf" class="btn btn-light btn-sm">
                                    <i class="fas fa-download me-2"></i>
                                    تحميل PDF
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <% if (issue.cover_image) { %>
                    <div class="col-lg-4 text-center">
                        <img src="/uploads/<%= issue.cover_image %>" 
                             alt="<%= issue.title %>" 
                             class="img-fluid rounded shadow-lg"
                             style="max-height: 400px;">
                    </div>
                <% } %>
            </div>
        </div>
    </section>

    <!-- Issue Content -->
    <section class="py-5">
        <div class="container">
            <% if (news && news.length > 0) { %>
                <div class="row">
                    <div class="col-12 mb-4">
                        <h2 class="h3 fw-bold text-center mb-4">
                            <i class="fas fa-newspaper text-primary me-2"></i>
                            محتويات العدد
                        </h2>
                    </div>
                </div>

                <div class="row">
                    <% news.forEach((item, index) => { %>
                        <div class="col-lg-6 mb-4">
                            <article class="news-item h-100">
                                <% if (item.image_path) { %>
                                    <img src="/uploads/<%= item.image_path %>" 
                                         alt="<%= item.title %>" 
                                         class="w-100 rounded mb-3"
                                         style="height: 250px; object-fit: cover;">
                                <% } %>
                                
                                <div class="news-content">
                                    <% if (item.category) { %>
                                        <span class="badge bg-primary mb-2">
                                            <i class="fas fa-tag me-1"></i>
                                            <%= item.category %>
                                        </span>
                                    <% } %>
                                    
                                    <h3 class="h4 fw-bold mb-3">
                                        <%= item.title %>
                                    </h3>
                                    
                                    <div class="news-text">
                                        <%= item.content.replace(/\n/g, '</p><p>') %>
                                    </div>
                                    
                                    <div class="news-meta">
                                        <small class="text-muted">
                                            <i class="fas fa-clock me-1"></i>
                                            <%= new Date(item.created_at).toLocaleDateString('ar-SA') %>
                                        </small>
                                    </div>
                                </div>
                            </article>
                        </div>
                    <% }); %>
                </div>
            <% } else { %>
                <div class="row">
                    <div class="col-12">
                        <div class="text-center py-5">
                            <i class="fas fa-newspaper fa-5x text-muted mb-4"></i>
                            <h3 class="text-muted mb-3">لا توجد أخبار في هذا العدد</h3>
                            <p class="text-muted">
                                هذا العدد لا يحتوي على أخبار حالياً. سيتم إضافة المحتوى قريباً.
                            </p>
                        </div>
                    </div>
                </div>
            <% } %>
        </div>
    </section>

    <!-- Navigation to other issues -->
    <section class="py-4 bg-light">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center">
                    <h4 class="fw-bold mb-3">
                        <i class="fas fa-archive me-2"></i>
                        تصفح أعداد أخرى
                    </h4>
                    <div class="d-flex justify-content-center gap-3">
                        <a href="/" class="btn btn-primary">
                            <i class="fas fa-home me-2"></i>
                            الصفحة الرئيسية
                        </a>
                        <a href="/archive" class="btn btn-outline-primary">
                            <i class="fas fa-archive me-2"></i>
                            أرشيف الأعداد
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5 class="fw-bold mb-3">
                        <i class="fas fa-newspaper me-2"></i>
                        المجلة الإلكترونية
                    </h5>
                    <p class="mb-0">
                        منصة إلكترونية لنشر أخبار ونشاطات المؤسسة بشكل دوري ومنظم
                    </p>
                </div>
                <div class="col-md-6 text-md-end">
                    <h6 class="fw-bold mb-3">تابعنا على</h6>
                    <div class="social-links">
                        <a href="#" class="text-white me-3">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="#" class="text-white me-3">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="text-white me-3">
                            <i class="fab fa-instagram"></i>
                        </a>
                        <a href="#" class="text-white">
                            <i class="fab fa-linkedin"></i>
                        </a>
                    </div>
                </div>
            </div>
            <hr class="my-4">
            <div class="row">
                <div class="col-12 text-center">
                    <p class="mb-0">
                        &copy; <%= new Date().getFullYear() %> جميع الحقوق محفوظة - المجلة الإلكترونية
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="/js/main.js"></script>

    <!-- Print Styles -->
    <style media="print">
        .navbar, .footer, .btn { display: none !important; }
        .container { max-width: 100% !important; }
        .news-item { break-inside: avoid; }
    </style>
</body>
</html>
