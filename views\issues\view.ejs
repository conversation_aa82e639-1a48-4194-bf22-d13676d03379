<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= issue.title %> - المجلة الإلكترونية</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Turn.js CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/turn.js/4.1.0/turn.min.css">
    <!-- Custom CSS -->
    <link href="/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
        <div class="container">
            <a class="navbar-brand fw-bold" href="/">
                <i class="fas fa-newspaper me-2"></i>
                المجلة الإلكترونية
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">
                            <i class="fas fa-home me-1"></i>
                            الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/archive">
                            <i class="fas fa-archive me-1"></i>
                            الأرشيف
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/about">
                            <i class="fas fa-info-circle me-1"></i>
                            حول المجلة
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/login">
                            <i class="fas fa-sign-in-alt me-1"></i>
                            لوحة التحكم
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Issue Header -->
    <section class="py-5" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <div class="text-white">
                        <h1 class="display-4 fw-bold mb-3">
                            <%= issue.title %>
                        </h1>
                        <% if (issue.description) { %>
                            <p class="lead mb-4">
                                <%= issue.description %>
                            </p>
                        <% } %>
                        <div class="d-flex align-items-center">
                            <div class="me-4">
                                <i class="fas fa-calendar me-2"></i>
                                <%= new Date(issue.issue_date).toLocaleDateString('ar-SA', { 
                                    year: 'numeric', 
                                    month: 'long', 
                                    day: 'numeric' 
                                }) %>
                            </div>
                            <div class="me-4">
                                <i class="fas fa-newspaper me-2"></i>
                                <%= news.length %> خبر
                            </div>
                            <div>
                                <button class="btn btn-light btn-sm me-2" onclick="window.print()">
                                    <i class="fas fa-print me-2"></i>
                                    طباعة
                                </button>
                                <a href="/issues/<%= issue.id %>/pdf" class="btn btn-light btn-sm">
                                    <i class="fas fa-download me-2"></i>
                                    تحميل PDF
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <% if (issue.cover_image) { %>
                    <div class="col-lg-4 text-center">
                        <img src="/uploads/<%= issue.cover_image %>" 
                             alt="<%= issue.title %>" 
                             class="img-fluid rounded shadow-lg"
                             style="max-height: 400px;">
                    </div>
                <% } %>
            </div>
        </div>
    </section>

    <!-- Magazine Container -->
    <section class="py-5">
        <div class="container-fluid">
            <div class="row justify-content-center">
                <div class="col-12">
                    <!-- Magazine Controls -->
                    <div class="text-center mb-4">
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-primary" id="prevPage">
                                <i class="fas fa-chevron-right me-2"></i>
                                الصفحة السابقة
                            </button>
                            <button type="button" class="btn btn-outline-secondary" id="pageInfo">
                                صفحة <span id="currentPage">1</span> من <span id="totalPages">1</span>
                            </button>
                            <button type="button" class="btn btn-outline-primary" id="nextPage">
                                الصفحة التالية
                                <i class="fas fa-chevron-left ms-2"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Magazine Book -->
                    <div class="magazine-container">
                        <div id="magazine" class="magazine">
                            <!-- Cover Page -->
                            <div class="page cover-page">
                                <div class="page-content">
                                    <div class="cover-design">
                                        <% if (issue.cover_image) { %>
                                            <img src="/uploads/<%= issue.cover_image %>"
                                                 alt="<%= issue.title %>"
                                                 class="cover-image">
                                        <% } %>
                                        <div class="cover-overlay">
                                            <h1 class="cover-title"><%= issue.title %></h1>
                                            <p class="cover-subtitle">المجلة الإلكترونية</p>
                                            <div class="cover-date">
                                                <%= new Date(issue.issue_date).toLocaleDateString('ar-SA', {
                                                    year: 'numeric',
                                                    month: 'long',
                                                    day: 'numeric'
                                                }) %>
                                            </div>
                                            <div class="cover-stats">
                                                <span class="badge bg-light text-dark">
                                                    <%= news.length %> خبر
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Table of Contents -->
                            <div class="page toc-page">
                                <div class="page-content">
                                    <h2 class="page-title">فهرس المحتويات</h2>
                                    <% if (issue.description) { %>
                                        <div class="issue-description">
                                            <h3>نبذة عن العدد</h3>
                                            <p><%= issue.description %></p>
                                        </div>
                                    <% } %>
                                    <div class="toc-list">
                                        <% if (news && news.length > 0) { %>
                                            <% news.forEach((item, index) => { %>
                                                <div class="toc-item">
                                                    <span class="toc-number"><%= index + 1 %></span>
                                                    <span class="toc-title"><%= item.title %></span>
                                                    <% if (item.category) { %>
                                                        <span class="toc-category"><%= item.category %></span>
                                                    <% } %>
                                                </div>
                                            <% }); %>
                                        <% } %>
                                    </div>
                                </div>
                            </div>

                            <!-- News Pages -->
                            <% if (news && news.length > 0) { %>
                                <% news.forEach((item, index) => { %>
                                    <div class="page news-page">
                                        <div class="page-content">
                                            <article class="news-article">
                                                <% if (item.category) { %>
                                                    <div class="news-category">
                                                        <span class="badge bg-primary">
                                                            <i class="fas fa-tag me-1"></i>
                                                            <%= item.category %>
                                                        </span>
                                                    </div>
                                                <% } %>

                                                <h1 class="news-title"><%= item.title %></h1>

                                                <% if (item.image_path) { %>
                                                    <div class="news-image">
                                                        <img src="/uploads/<%= item.image_path %>"
                                                             alt="<%= item.title %>"
                                                             class="img-fluid rounded">
                                                    </div>
                                                <% } %>

                                                <div class="news-content">
                                                    <%
                                                        const paragraphs = item.content.split('\n').filter(p => p.trim());
                                                        paragraphs.forEach(paragraph => {
                                                    %>
                                                        <p><%= paragraph %></p>
                                                    <% }); %>
                                                </div>

                                                <div class="news-meta">
                                                    <small class="text-muted">
                                                        <i class="fas fa-clock me-1"></i>
                                                        <%= new Date(item.created_at).toLocaleDateString('ar-SA') %>
                                                    </small>
                                                </div>
                                            </article>

                                            <div class="page-number">
                                                <%= index + 3 %>
                                            </div>
                                        </div>
                                    </div>
                                <% }); %>
                            <% } %>

                            <!-- Back Cover -->
                            <div class="page back-cover">
                                <div class="page-content">
                                    <div class="back-cover-content">
                                        <h2>شكراً لقراءتكم</h2>
                                        <p>نتطلع لمشاركتكم المزيد من الأخبار والفعاليات في الأعداد القادمة</p>
                                        <div class="social-links">
                                            <h4>تابعونا على</h4>
                                            <div class="social-icons">
                                                <a href="#" class="social-icon">
                                                    <i class="fab fa-facebook-f"></i>
                                                </a>
                                                <a href="#" class="social-icon">
                                                    <i class="fab fa-twitter"></i>
                                                </a>
                                                <a href="#" class="social-icon">
                                                    <i class="fab fa-instagram"></i>
                                                </a>
                                                <a href="#" class="social-icon">
                                                    <i class="fab fa-linkedin"></i>
                                                </a>
                                            </div>
                                        </div>
                                        <div class="copyright">
                                            <p>&copy; <%= new Date().getFullYear() %> جميع الحقوق محفوظة</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Navigation to other issues -->
    <section class="py-4 bg-light">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center">
                    <h4 class="fw-bold mb-3">
                        <i class="fas fa-archive me-2"></i>
                        تصفح أعداد أخرى
                    </h4>
                    <div class="d-flex justify-content-center gap-3">
                        <a href="/" class="btn btn-primary">
                            <i class="fas fa-home me-2"></i>
                            الصفحة الرئيسية
                        </a>
                        <a href="/archive" class="btn btn-outline-primary">
                            <i class="fas fa-archive me-2"></i>
                            أرشيف الأعداد
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5 class="fw-bold mb-3">
                        <i class="fas fa-newspaper me-2"></i>
                        المجلة الإلكترونية
                    </h5>
                    <p class="mb-0">
                        منصة إلكترونية لنشر أخبار ونشاطات المؤسسة بشكل دوري ومنظم
                    </p>
                </div>
                <div class="col-md-6 text-md-end">
                    <h6 class="fw-bold mb-3">تابعنا على</h6>
                    <div class="social-links">
                        <a href="#" class="text-white me-3">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="#" class="text-white me-3">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="text-white me-3">
                            <i class="fab fa-instagram"></i>
                        </a>
                        <a href="#" class="text-white">
                            <i class="fab fa-linkedin"></i>
                        </a>
                    </div>
                </div>
            </div>
            <hr class="my-4">
            <div class="row">
                <div class="col-12 text-center">
                    <p class="mb-0">
                        &copy; <%= new Date().getFullYear() %> جميع الحقوق محفوظة - المجلة الإلكترونية
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Turn.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/turn.js/4.1.0/turn.min.js"></script>
    <!-- Custom JS -->
    <script src="/js/main.js"></script>

    <!-- Magazine Styles -->
    <style>
        .magazine-container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 600px;
            background: #f8f9fa;
            padding: 20px;
            border-radius: 15px;
        }

        .magazine {
            width: 800px;
            height: 600px;
            background: white;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            border-radius: 10px;
            overflow: hidden;
        }

        .page {
            width: 400px;
            height: 600px;
            background: white;
            padding: 30px;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            position: relative;
            border: 1px solid #e0e0e0;
        }

        .page-content {
            flex: 1;
            overflow: hidden;
        }

        /* Cover Page Styles */
        .cover-page {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            padding: 0;
        }

        .cover-design {
            height: 100%;
            position: relative;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .cover-image {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            opacity: 0.3;
        }

        .cover-overlay {
            position: relative;
            z-index: 2;
            padding: 40px;
        }

        .cover-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }

        .cover-subtitle {
            font-size: 1.2rem;
            margin-bottom: 30px;
            opacity: 0.9;
        }

        .cover-date {
            font-size: 1rem;
            margin-bottom: 20px;
            opacity: 0.8;
        }

        /* Table of Contents */
        .toc-page {
            background: #f8f9fa;
        }

        .page-title {
            color: #2c3e50;
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 30px;
            text-align: center;
            border-bottom: 3px solid #667eea;
            padding-bottom: 15px;
        }

        .issue-description {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            border-right: 5px solid #667eea;
        }

        .issue-description h3 {
            color: #667eea;
            font-size: 1.3rem;
            margin-bottom: 15px;
        }

        .toc-list {
            background: white;
            border-radius: 10px;
            padding: 20px;
        }

        .toc-item {
            display: flex;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .toc-item:last-child {
            border-bottom: none;
        }

        .toc-number {
            background: #667eea;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-left: 15px;
            flex-shrink: 0;
        }

        .toc-title {
            flex: 1;
            font-weight: 600;
            color: #2c3e50;
        }

        .toc-category {
            background: #e9ecef;
            color: #6c757d;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8rem;
        }

        /* News Page Styles */
        .news-page {
            background: white;
        }

        .news-category {
            margin-bottom: 20px;
        }

        .news-title {
            color: #2c3e50;
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 20px;
            line-height: 1.3;
        }

        .news-image {
            margin: 20px 0;
            text-align: center;
        }

        .news-image img {
            max-width: 100%;
            max-height: 200px;
            object-fit: cover;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .news-content {
            flex: 1;
            font-size: 1rem;
            line-height: 1.6;
            color: #495057;
        }

        .news-content p {
            margin-bottom: 15px;
            text-align: justify;
        }

        .news-meta {
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px solid #e9ecef;
        }

        /* Back Cover */
        .back-cover {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            text-align: center;
        }

        .back-cover-content {
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .back-cover h2 {
            font-size: 2rem;
            margin-bottom: 20px;
        }

        .social-links {
            margin: 30px 0;
        }

        .social-links h4 {
            margin-bottom: 20px;
        }

        .social-icons {
            display: flex;
            gap: 15px;
            justify-content: center;
        }

        .social-icon {
            width: 50px;
            height: 50px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .social-icon:hover {
            background: rgba(255,255,255,0.3);
            color: white;
            transform: translateY(-3px);
        }

        /* Page Number */
        .page-number {
            position: absolute;
            bottom: 15px;
            left: 50%;
            transform: translateX(-50%);
            background: #667eea;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 0.9rem;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .magazine {
                width: 100%;
                max-width: 400px;
                height: 500px;
            }

            .page {
                width: 100%;
                height: 500px;
                padding: 20px;
            }

            .cover-title {
                font-size: 1.8rem;
            }

            .news-title {
                font-size: 1.4rem;
            }
        }

        /* Print Styles */
        @media print {
            .navbar, .footer, .btn-group { display: none !important; }
            .magazine-container { background: white; }
            .page { page-break-after: always; }
        }
    </style>

    <!-- Magazine JavaScript -->
    <script>
        $(document).ready(function() {
            // Initialize Turn.js
            $("#magazine").turn({
                width: 800,
                height: 600,
                autoCenter: true,
                direction: 'rtl'
            });

            // Update page info
            function updatePageInfo() {
                const currentPage = $("#magazine").turn("page");
                const totalPages = $("#magazine").turn("pages");
                $("#currentPage").text(currentPage);
                $("#totalPages").text(totalPages);

                // Update button states
                $("#prevPage").prop('disabled', currentPage <= 1);
                $("#nextPage").prop('disabled', currentPage >= totalPages);
            }

            // Page navigation
            $("#prevPage").click(function() {
                $("#magazine").turn("previous");
            });

            $("#nextPage").click(function() {
                $("#magazine").turn("next");
            });

            // Update page info on turn
            $("#magazine").bind("turned", function(event, page, view) {
                updatePageInfo();
            });

            // Initial page info update
            updatePageInfo();

            // Keyboard navigation
            $(document).keydown(function(e) {
                if (e.keyCode == 37) { // Left arrow
                    $("#magazine").turn("next");
                } else if (e.keyCode == 39) { // Right arrow
                    $("#magazine").turn("previous");
                }
            });

            // Responsive handling
            function resizeMagazine() {
                const windowWidth = $(window).width();
                if (windowWidth < 768) {
                    $("#magazine").turn("size", 400, 500);
                } else {
                    $("#magazine").turn("size", 800, 600);
                }
            }

            $(window).resize(resizeMagazine);
            resizeMagazine();
        });
    </script>
</body>
</html>
