{"name": "magazin", "version": "1.0.0", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["magazine", "cms", "news", "publishing"], "author": "Magazine Team", "license": "ISC", "description": "منصة إلكترونية ديناميكية لإدارة ونشر المجلات الإلكترونية المؤسسية", "dependencies": {"bcryptjs": "^3.0.2", "bootstrap": "^5.3.7", "ejs": "^3.1.10", "express": "^5.1.0", "express-session": "^1.18.1", "express-validator": "^7.2.1", "html-pdf": "^3.0.1", "multer": "^2.0.1", "nodemailer": "^7.0.5", "puppeteer": "^24.13.0", "sqlite3": "^5.1.7"}}