{"name": "hasha", "version": "2.2.0", "description": "Hashing made simple. Get the hash of a buffer/string/stream/file.", "license": "MIT", "repository": "sindresorhus/hasha", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["hash", "hashing", "crypto", "hex", "base64", "md5", "sha1", "sha256", "sha512", "sum", "stream", "file", "fs", "buffer", "string", "text", "rev", "revving", "simple", "easy"], "dependencies": {"is-stream": "^1.0.1", "pinkie-promise": "^2.0.0"}, "devDependencies": {"ava": "*", "proxyquire": "^1.7.2", "xo": "*"}}