{"name": "html-pdf", "version": "3.0.1", "description": "HTML to PDF converter that uses phantomjs", "engines": {"node": ">=4.0.0"}, "main": "lib/index.js", "directories": {"test": "test"}, "bin": {"html-pdf": "bin/index.js"}, "scripts": {"test": "standard && tape test/index.js examples/*/test.js"}, "author": "<PERSON>", "license": "MIT", "devDependencies": {"phantomjs-prebuilt": "^2.1.16", "standard": "^5.1.1", "tap-spec": "^2.2.0", "tape": "^3.4.0"}, "optionalDependencies": {"phantomjs-prebuilt": "^2.1.16"}, "repository": {"type": "git", "url": "git://github.com/ma<PERSON><PERSON><PERSON>/node-html-pdf.git"}, "keywords": ["html", "pdf", "phantom", "phantom<PERSON>s", "nodejs"], "bugs": {"url": "https://github.com/ma<PERSON><PERSON><PERSON>/node-html-pdf/issues"}, "homepage": "https://github.com/ma<PERSON><PERSON><PERSON>/node-html-pdf"}