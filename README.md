# 📰 المجلة الإلكترونية المؤسسية

منصة إلكترونية ديناميكية متكاملة لإدارة ونشر المجلات الإلكترونية المؤسسية مع تجربة قراءة تفاعلية تحاكي المجلات الحقيقية.

## ✨ المميزات الرئيسية

### 🎯 إدارة شاملة
- **لوحة تحكم متقدمة** مع واجهة عربية سهلة الاستخدام
- **نظام صلاحيات متعدد المستويات** (مدير، محرر، مشاهد)
- **إدارة المستخدمين** مع تتبع النشاطات
- **إدارة الأخبار** مع دعم الصور والفئات
- **إدارة الأعداد** مع أغلفة مخصصة

### 📖 تجربة قراءة مميزة
- **تأثير تقليب الصفحات** باستخدام Turn.js
- **تصميم متجاوب** يعمل على جميع الأجهزة
- **واجهة مجلة حقيقية** مع غلاف وفهرس محتويات
- **تنقل سهل** بالأزرار أو لوحة المفاتيح

### 📄 تصدير ومشاركة
- **تصدير PDF عالي الجودة** باستخدام Puppeteer
- **مشاركة على وسائل التواصل** الاجتماعي
- **روابط مباشرة** لكل عدد
- **أرشيف شامل** لجميع الأعداد

### 📧 نظام الاشتراك
- **اشتراك بالبريد الإلكتروني** للحصول على الأعداد الجديدة
- **إدارة المشتركين** من لوحة التحكم
- **إلغاء اشتراك سهل** بنقرة واحدة

## 🛠️ التقنيات المستخدمة

### Backend
- **Node.js** - الخادم الخلفي
- **Express.js** - إطار العمل
- **SQLite** - قاعدة البيانات
- **EJS** - محرك القوالب

### Frontend
- **Bootstrap 5** - التصميم المتجاوب
- **Font Awesome** - الأيقونات
- **Turn.js** - تأثير تقليب الصفحات
- **jQuery** - التفاعلات

### أدوات إضافية
- **Puppeteer** - توليد ملفات PDF
- **Multer** - رفع الملفات
- **bcryptjs** - تشفير كلمات المرور
- **express-session** - إدارة الجلسات

## 🚀 التثبيت والتشغيل

### المتطلبات
- Node.js (الإصدار 14 أو أحدث)
- npm أو yarn

### خطوات التثبيت

1. **استنساخ المشروع**
```bash
git clone [repository-url]
cd MAGAzIN
```

2. **تثبيت التبعيات**
```bash
npm install
```

3. **تشغيل المشروع**
```bash
npm start
```

4. **الوصول للموقع**
- الموقع الرئيسي: `http://localhost:3000`
- لوحة التحكم: `http://localhost:3000/admin/login`

### بيانات الدخول الافتراضية
- **اسم المستخدم:** admin
- **كلمة المرور:** admin123

## 📁 هيكل المشروع

```
MAGAzIN/
├── app.js                 # الملف الرئيسي للخادم
├── package.json           # تبعيات المشروع
├── magazine.db           # قاعدة البيانات (تُنشأ تلقائياً)
├── public/               # الملفات الثابتة
│   ├── css/
│   │   └── style.css     # التنسيقات المخصصة
│   └── js/
│       └── main.js       # JavaScript المخصص
├── uploads/              # الصور المرفوعة
├── views/                # قوالب EJS
│   ├── admin/            # صفحات لوحة التحكم
│   │   ├── dashboard.ejs
│   │   ├── login.ejs
│   │   ├── news/
│   │   └── issues/
│   ├── issues/           # صفحات عرض الأعداد
│   │   ├── view.ejs
│   │   └── pdf.ejs
│   ├── index.ejs         # الصفحة الرئيسية
│   ├── archive.ejs       # صفحة الأرشيف
│   ├── about.ejs         # صفحة حول المجلة
│   └── subscribe.ejs     # صفحة الاشتراك
└── README.md
```

## 🎮 كيفية الاستخدام

### 1. إدارة المستخدمين
- سجل دخول كمدير
- انتقل لقسم "المستخدمين"
- أضف محررين جدد مع تحديد الصلاحيات

### 2. إضافة الأخبار
- انتقل لقسم "الأخبار"
- اضغط "إضافة خبر جديد"
- أدخل العنوان والمحتوى
- ارفع صورة (اختياري)
- حدد الفئة والعدد المرتبط

### 3. إنشاء عدد جديد
- انتقل لقسم "الأعداد"
- اضغط "إنشاء عدد جديد"
- أدخل عنوان العدد ووصفه
- ارفع صورة الغلاف
- احفظ العدد

### 4. ربط الأخبار بالأعداد
- عدّل الأخبار الموجودة
- اختر العدد المناسب من القائمة المنسدلة
- احفظ التغييرات

### 5. نشر العدد
- انتقل لقائمة الأعداد
- اضغط زر "نشر" للعدد المطلوب
- سيصبح العدد متاحاً للجمهور

## 🌐 الصفحات المتاحة

### للجمهور العام
- `/` - الصفحة الرئيسية
- `/archive` - أرشيف جميع الأعداد
- `/issues/:id` - عرض عدد محدد
- `/issues/:id/pdf` - تحميل العدد كـ PDF
- `/about` - حول المجلة
- `/subscribe` - الاشتراك بالبريد الإلكتروني

### لوحة التحكم
- `/admin/login` - تسجيل الدخول
- `/admin/dashboard` - لوحة التحكم الرئيسية
- `/admin/news` - إدارة الأخبار
- `/admin/issues` - إدارة الأعداد
- `/admin/subscribers` - إدارة المشتركين (للمدراء فقط)

## 🔧 التخصيص

### تغيير التصميم
- عدّل ملف `public/css/style.css`
- استخدم متغيرات CSS للألوان الأساسية
- خصص قوالب EJS حسب احتياجاتك

### إضافة ميزات جديدة
- أضف routes جديدة في `app.js`
- أنشئ قوالب EJS مناسبة
- حدّث قاعدة البيانات حسب الحاجة

## 📊 قاعدة البيانات

### الجداول الرئيسية
- **users** - المستخدمين والصلاحيات
- **news** - الأخبار والمحتوى
- **issues** - أعداد المجلة
- **subscribers** - المشتركين بالبريد الإلكتروني

## 🔒 الأمان

- تشفير كلمات المرور باستخدام bcrypt
- حماية الجلسات مع express-session
- التحقق من صلاحيات المستخدمين
- تنظيف وتحقق من البيانات المدخلة

## 🚀 النشر

### النشر المحلي
```bash
npm start
```

### النشر على الخادم
1. ارفع الملفات للخادم
2. ثبت Node.js و npm
3. شغل `npm install`
4. استخدم PM2 للتشغيل المستمر:
```bash
npm install -g pm2
pm2 start app.js --name magazine
```

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. إجراء التغييرات المطلوبة
4. إرسال Pull Request

## 📝 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## 📞 الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل:
- أنشئ Issue جديد في GitHub
- راسلنا على البريد الإلكتروني

---

**تم تطوير هذا المشروع بـ ❤️ لخدمة المؤسسات العربية**
