<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أرشيف الأعداد - المجلة الإلكترونية</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
        <div class="container">
            <a class="navbar-brand fw-bold" href="/">
                <i class="fas fa-newspaper me-2"></i>
                المجلة الإلكترونية
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">
                            <i class="fas fa-home me-1"></i>
                            الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/archive">
                            <i class="fas fa-archive me-1"></i>
                            الأرشيف
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/about">
                            <i class="fas fa-info-circle me-1"></i>
                            حول المجلة
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/login">
                            <i class="fas fa-sign-in-alt me-1"></i>
                            لوحة التحكم
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="py-5" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center text-white">
                    <h1 class="display-4 fw-bold mb-3">
                        <i class="fas fa-archive me-3"></i>
                        أرشيف الأعداد
                    </h1>
                    <p class="lead mb-0">
                        تصفح جميع أعداد المجلة الإلكترونية المنشورة
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Archive Content -->
    <section class="py-5">
        <div class="container">
            <% if (issues && issues.length > 0) { %>
                <!-- Statistics -->
                <div class="row mb-5">
                    <div class="col-12 text-center">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <div class="card border-0 shadow-sm h-100">
                                    <div class="card-body text-center">
                                        <div class="mb-3">
                                            <i class="fas fa-book fa-3x text-primary"></i>
                                        </div>
                                        <h3 class="fw-bold text-primary"><%= issues.length %></h3>
                                        <p class="text-muted mb-0">إجمالي الأعداد</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="card border-0 shadow-sm h-100">
                                    <div class="card-body text-center">
                                        <div class="mb-3">
                                            <i class="fas fa-newspaper fa-3x text-success"></i>
                                        </div>
                                        <h3 class="fw-bold text-success">
                                            <%= issues.reduce((total, issue) => total + issue.news_count, 0) %>
                                        </h3>
                                        <p class="text-muted mb-0">إجمالي الأخبار</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="card border-0 shadow-sm h-100">
                                    <div class="card-body text-center">
                                        <div class="mb-3">
                                            <i class="fas fa-calendar fa-3x text-warning"></i>
                                        </div>
                                        <h3 class="fw-bold text-warning">
                                            <%= new Date().getFullYear() %>
                                        </h3>
                                        <p class="text-muted mb-0">السنة الحالية</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Issues Grid -->
                <div class="row">
                    <div class="col-12 mb-4">
                        <h2 class="h3 fw-bold text-center mb-4">
                            <i class="fas fa-list me-2"></i>
                            جميع الأعداد
                        </h2>
                    </div>
                </div>

                <div class="row">
                    <% issues.forEach((issue, index) => { %>
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="card h-100 border-0 shadow-sm">
                                <% if (issue.cover_image) { %>
                                    <img src="/uploads/<%= issue.cover_image %>" 
                                         class="card-img-top" 
                                         alt="<%= issue.title %>"
                                         style="height: 250px; object-fit: cover;">
                                <% } else { %>
                                    <div class="card-img-top d-flex align-items-center justify-content-center bg-light" 
                                         style="height: 250px;">
                                        <i class="fas fa-book fa-4x text-muted"></i>
                                    </div>
                                <% } %>
                                
                                <div class="card-body d-flex flex-column">
                                    <h5 class="card-title fw-bold">
                                        <%= issue.title %>
                                    </h5>
                                    
                                    <% if (issue.description) { %>
                                        <p class="card-text text-muted flex-grow-1">
                                            <%= issue.description.substring(0, 120) %>
                                            <% if (issue.description.length > 120) { %>...<%}%>
                                        </p>
                                    <% } %>
                                    
                                    <div class="mt-auto">
                                        <div class="d-flex justify-content-between align-items-center mb-3">
                                            <small class="text-muted">
                                                <i class="fas fa-calendar me-1"></i>
                                                <%= new Date(issue.issue_date).toLocaleDateString('ar-SA') %>
                                            </small>
                                            <span class="badge bg-primary rounded-pill">
                                                <i class="fas fa-newspaper me-1"></i>
                                                <%= issue.news_count %> خبر
                                            </span>
                                        </div>
                                        
                                        <div class="d-grid gap-2">
                                            <a href="/issues/<%= issue.id %>" class="btn btn-primary">
                                                <i class="fas fa-book-open me-2"></i>
                                                قراءة العدد
                                            </a>
                                            <div class="btn-group" role="group">
                                                <a href="/issues/<%= issue.id %>/pdf" 
                                                   class="btn btn-outline-secondary btn-sm">
                                                    <i class="fas fa-download me-1"></i>
                                                    PDF
                                                </a>
                                                <button class="btn btn-outline-secondary btn-sm" 
                                                        onclick="shareIssue('<%= issue.id %>', '<%= issue.title %>')">
                                                    <i class="fas fa-share me-1"></i>
                                                    مشاركة
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <% }); %>
                </div>
            <% } else { %>
                <div class="row">
                    <div class="col-12">
                        <div class="text-center py-5">
                            <i class="fas fa-archive fa-5x text-muted mb-4"></i>
                            <h3 class="text-muted mb-3">لا توجد أعداد منشورة</h3>
                            <p class="text-muted">
                                لم يتم نشر أي أعداد حتى الآن. تابعونا للحصول على آخر التحديثات.
                            </p>
                            <a href="/" class="btn btn-primary mt-3">
                                <i class="fas fa-home me-2"></i>
                                العودة للرئيسية
                            </a>
                        </div>
                    </div>
                </div>
            <% } %>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5 class="fw-bold mb-3">
                        <i class="fas fa-newspaper me-2"></i>
                        المجلة الإلكترونية
                    </h5>
                    <p class="mb-0">
                        منصة إلكترونية لنشر أخبار ونشاطات المؤسسة بشكل دوري ومنظم
                    </p>
                </div>
                <div class="col-md-6 text-md-end">
                    <h6 class="fw-bold mb-3">تابعنا على</h6>
                    <div class="social-links">
                        <a href="#" class="text-white me-3">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="#" class="text-white me-3">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="text-white me-3">
                            <i class="fab fa-instagram"></i>
                        </a>
                        <a href="#" class="text-white">
                            <i class="fab fa-linkedin"></i>
                        </a>
                    </div>
                </div>
            </div>
            <hr class="my-4">
            <div class="row">
                <div class="col-12 text-center">
                    <p class="mb-0">
                        &copy; <%= new Date().getFullYear() %> جميع الحقوق محفوظة - المجلة الإلكترونية
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="/js/main.js"></script>
    
    <script>
        function shareIssue(issueId, issueTitle) {
            const url = window.location.origin + '/issues/' + issueId;
            
            if (navigator.share) {
                navigator.share({
                    title: issueTitle,
                    text: 'اقرأ هذا العدد من المجلة الإلكترونية',
                    url: url
                });
            } else {
                // Fallback: copy to clipboard
                navigator.clipboard.writeText(url).then(() => {
                    alert('تم نسخ رابط العدد إلى الحافظة');
                });
            }
        }
    </script>
</body>
</html>
